const { IView } = require('../../../component/iview');
const { Biz<PERSON>elper } = require('../../../libs/helper-biz');
const { AggregatedPosition } = require('./records');
const { AccountSimple } = require('../../20cm/components/objects');
const { LabelValue, SellTask, TaskObject, TaskStatus } = require('../objects');
const { Cm20FunctionCodes } = require('../../../config/20cm');
const { repoInstrument } = require('../../../repository/instrument');
const { repoAccount } = require('../../../repository/account');

/**
 * @returns {Array<LabelValue>}
 */
function MakeLevels(count) {

    var levels = [];

    // for (let idx = count; idx >= 1; idx--) {
    //     levels.push(new LabelValue('卖' + idx, -idx));
    // }

    for (let idx = 1; idx <= count; idx++) {
        levels.push(new LabelValue('买' + idx, idx));
    }

    return levels;
}

/**
 * @returns {AggregatedPosition}
 */
function MakeAggregatedPosition() {
    return null;
}

/**
 * @returns {TaskObject}
 */
function MakeAutoSellTask() {
    return null;
}

module.exports = class RecordsView extends IView {

    constructor() {

        super('@20cm-july/main/trading', false, '交易');
        this.defaults = { customRatio: 20 };
        this.accounts = [new AccountSimple({})].splice(1);
        this.modes = { mannual: 1, market: 2, auto: 3 };
        this.directions = { buy: 1, sell: -1 };
        this.levels = MakeLevels(5);

        this.marketPriceTypes = [
            { label: '对手方最优价格', value: 'counter-party-best' },
        ];
        
        this.strategyMap = {

            intervally: { label: '定时定量', value: 101 },
            lowControl: { label: '低封单量', value: 103 },
            fixed: { label: '定涨定量', value: 108 },
        };

        this.strategies = [

            this.strategyMap.intervally,
            this.strategyMap.lowControl,
            this.strategyMap.fixed,
        ];

        this.tsellsMap = {};

        /**
         * 持仓请求，数据到达，回调
         */
        this.positionCallbacks = [function(position = new AggregatedPosition){}].splice(1);

        /**
         * 自动卖出任务，数据到达，回调
         */
        this.sellTaskCallbacks = [function(task = new SellTask({})){}].splice(1);
        /** 市场代码 */
        this.markets = { sh: 'shse', sz: 'szse' };
    }

    get autoTsk() {
        return this.states.autoTsk;
    }

    get vueLevels() {
        if (this.isFixed()) {
            return [ {label: '卖一', value: -1} ].concat(this.levels)
        } else {
            return this.levels
        }
    }

    get maxLimit() {
        let ins = this.auto.instrument;
        if (!ins) {
          return 10;
        }
        let max20Endwiths = [ '.3', '.688', '.689' ];
        return max20Endwiths.some(function(endwith) {
            return ins.indexOf(endwith) > 0;
        }) ? 20 : 10;
    }

    get limitPlaceholder() {
      return `比例：-${this.maxLimit} ~ ${this.maxLimit}`;
    }

    readSetCustomRatio(ratio) {

        let key = 'manual-trade-custom-rate';
        if (typeof ratio == 'number' && ratio >= 1) {
            localStorage.setItem(key, ratio);
        }

        let recent = parseFloat(localStorage.getItem(key));
        return recent >= 1 ? recent : this.defaults.customRatio;
    }

    createApp() {

        this.states = {

            isCreditAccount: false,
            availableCash: null,
            focused: this.modes.mannual,
            linkedPosition: MakeAggregatedPosition(),
            autoTsk: MakeAutoSellTask(),
        };

        this.mannual = {

            instrument: null,
            instrumentName: null,
            direction: this.directions.buy,
            keywords: null,
            marketPriceType: this.marketPriceTypes[0].value,
            price: 0,
            ceiling: 0,
            floor: 0,
            volume: 0,
            amount: 0,
            isByVolume: true,
            estimated: null,
            lowerSellLimit: 0,
            upperBuyLimit: 0,
            customRatio: this.readSetCustomRatio(),
            isEditingCustomRatio: false,
        };

        this.auto = {

            instrument: null,
            instrumentName: null,
            keywords: null,
            strategy: this.strategyMap.intervally.value,
            percent: 1,
            level: null,
            volume: 0,
            cancel: {

                /** 撤单保护，是否启用 */
                enabled: false, 
                /** 撤单保护市场，单位秒 */
                time: null,
            },
            raiseOffset: 0,
            raiseRateLimit: 0,
        };

        const $vapp = new Vue({

            el: this.$container.firstElementChild,
            data: {

                states: this.states,
                modes: this.modes,
                directions: this.directions,
                strategies: this.strategies,
                
                marketPriceTypes: this.marketPriceTypes,
                mannual: this.mannual,
                auto: this.auto,
            },
            computed: {

                properStep: () => {
                    return this.isLowBuy1VolumeStrategy() ? 1 : 100;
                },
                levels:() => this.vueLevels,
                maxLimit: () => this.maxLimit,
                limitPlaceholder: () => this.limitPlaceholder
            },
            watch: {

                'mannual.price': () => {
                    if (this.mannual.isByVolume) {
                        this.estimateCost(this.mannual.price, this.mannual.volume);
                    }
                    else {
                        this.estimateVoume(this.mannual.price, this.mannual.amount);
                    }
                },

                'mannual.volume': () => {
                    this.estimateCost(this.mannual.price, this.mannual.volume);
                },

                'mannual.amount': () => {
                    this.estimateVoume(this.mannual.price, this.mannual.amount);
                },
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleModeChange,
                this.handleDirectionChange,
                this.isMannualMode,
                this.isByMarketPrice,
                this.isAutoMode,
                this.isMannualBuy,

                this.handleSuggest,
                this.handleInput,
                this.handleSelect,
                this.handleClear,

                this.setAsPrice,
                this.setByRatio,
                this.setByCustomRatio,
                this.mbuy,
                this.mcredit,
                this.msell,
                this.startAuto,
                this.stopAuto,
                this.isAutoRunning,
                this.handleParamChange,
                this.handleStrategyChange,
                this.getPositionCondition,
                this.isLowBuy1VolumeStrategy,
                this.isFixed,
                this.isIntervally,
                this.calculateByPrice,
                this.calculateByVolume,
                this.calculateByAmount,
                this.toggleMethod,
                this.precisePrice,
                this.decidePriceStep,
                this.handleCustomRatioChange,
                this.handleCustomRatioBlur,
            ]),
        });
    }

    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(this.getPricePrecision()) : price;
    }

    decidePriceStep() {
        return this.getPricePrecision() == 3 ? 0.001 : 0.01;
    }

    getPricePrecision() {
        
        let instrument = this.isMannualMode() ? this.mannual.instrument : this.auto.instrument;
        return BizHelper.getPricePrecision(instrument);
    }

    calculateByPrice($event) {

        let price = parseFloat($event.target.value);
        if (this.mannual.isByVolume) {
            this.estimateCost(price, this.mannual.volume);
        }
        else {
            this.estimateVoume(price, this.mannual.amount);
        }
    }

    calculateByVolume($event) {
        this.estimateCost(this.mannual.price, parseFloat($event.target.value));
    }

    calculateByAmount($event) {
        this.estimateVoume(this.mannual.price, parseFloat($event.target.value));
    }

    estimateCost(price, volume) {

        if (price > 0 && volume > 0) {

            let amount = price * volume;
            let result;

            if (amount < 10000) {
                result = amount.toFixed(0);
            }
            else if (amount < 100000) {
                result = (amount / 10000).toFixed(1) + '万';
            }
            else if (amount < 100000000) {
                result = (amount / 10000).toFixed(0) + '万';
            }
            else {
                result = (amount / 100000000).toFixed(2) + '亿';
            }

            this.mannual.estimated = `预估 ${result}`;
        }
        else {
            this.mannual.estimated = null;
        }
    }

    transferAmount2Volume(price, amount) {
        return price > 0 && amount > 0 ? Math.floor(amount / price / 100) * 100 : 0;
    }

    estimateVoume(price, amount) {

        if (price > 0 && amount > 0) {

            let volume = this.transferAmount2Volume(price, amount);
            let result;

            if (volume < 10000) {
                result = volume + '股';
            }
            else {
                result = (volume / 100) + '手';
            }

            this.mannual.estimated = `预估 ${result}`;
        }
        else {
            this.mannual.estimated = null;
        }
    }

    toggleMethod() {

        if (this.isMannualSell()) {
            return;
        }

        const ref = this.mannual;
        ref.isByVolume = !ref.isByVolume;

        if (ref.isByVolume) {
            this.estimateCost(ref.price, ref.volume);
        }
        else {
            this.estimateVoume(ref.price, ref.amount);
        }
    }

    /**
     * @param {TaskObject} task 
     */
    handleSellTask(task) {

        this.states.focused = this.modes.auto;
        this.handleClear();
        setTimeout(() => {

            // 回显合约
            this.setAsInstrument(task.instrument, task.instrumentName);
            this.trigger('set-instrument', task.instrument, task.instrumentName);
            // 回显其他
            this.auto.strategy = task.boardStrategy.strategyType;
            this.auto.percent = task.positionPercent;
            this.auto.level = task.priceFollowType;
            this.auto.volume = task.boardStrategy.strategyVolume;
            this.auto.cancel.enabled = task.cancelCondition.cancelProtectedEnabled;
            this.auto.cancel.time = task.cancelCondition.cancelProtectedTime;
            this.auto.raiseRateLimit = task.boardStrategy.raiseRateLimit / 10000;
            this.auto.raiseOffset = task.boardStrategy.raiseOffset / 10000;
        }, 20);
    }

    handleModeChange() {
        this.handleClear();
    }

    handleDirectionChange() {
        this.mannual.isByVolume = true;
    }

    isMannualMode() {
        return this.states.focused == this.modes.mannual || this.states.focused == this.modes.market;
    }

    isByLimitedPrice() {
        return this.states.focused == this.modes.mannual;
    }

    isByMarketPrice() {
        return this.states.focused == this.modes.market;
    }

    isAutoMode() {
        return this.states.focused == this.modes.auto;
    }

    isMannualBuy() {
        return this.isMannualMode() && this.mannual.direction == this.directions.buy;
    }

    isMannualSell() {
        return this.isMannualMode() && this.mannual.direction == this.directions.sell;
    }

    handleInput() {

        const ref = this.isMannualMode() ? this.mannual : this.auto;

        if (event.keyCode == 8) {

            event.returnValue = false;
            ref.keywords = null;
            this.handleClear();
        }
        else if (typeof ref.keywords == 'string' && ref.keywords.trim().length == 0) {
            this.handleClear();
        }
    }

    handleClear() {

        this.linkPosition(null);
        this.setAsInstrument(null, null);
        this.trigger('set-instrument', null, null);
    }

    /**
     * @param {String} keywords 
     * @param {Function} callback 
     */
    handleSuggest(keywords, callback) {

        if (typeof keywords != 'string' || keywords.trim().length < 1) {

            callback([]);
            return;
        }

        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, keywords);
        if (matches.length == 1) {

            callback([]);
            this.handleSelect(matches[0]);
            return;
        }

        callback(matches);
    }

    handleSelect(selected) {

        var { instrument, instrumentName } = selected;
        this.setAsInstrument(instrument, instrumentName);
        this.trigger('set-instrument', instrument, instrumentName);
    }

    /**
     * @param {String} instrument 
     */
    shortizeCode(instrument) {
        return instrument.split('.')[1];
    }

    async setAsInstrument(instrument, instrumentName) {

        // 根据所选合约所属市场，查询其所属市场账号是否为信用账号
        this.states.isCreditAccount = this.isCreditAccount(instrument);
        this.log(`set stock to: ${instrument}/${instrumentName}, mode = ${this.states.focused}`);
        var keywords = instrument ? `${this.shortizeCode(instrument)}-${instrumentName}` : null;

        if (this.isMannualMode()) {

            let ref = this.mannual;
            ref.keywords = keywords;
            ref.instrument = instrument;
            ref.instrumentName = instrumentName;
        }
        else {

            let ref = this.auto;
            ref.keywords = keywords;
            ref.instrument = instrument;
            ref.instrumentName = instrumentName;
            ref.strategy = this.strategyMap.intervally.value;
            ref.percent = 1;
            ref.level = this.vueLevels[0].value;
            ref.volume = 0;
            ref.cancel.enabled = false;
            ref.cancel.time = null;
        }

        let result = await this.requestPriceInfo(instrument);
        let ref2 = this.mannual;
        this.setAsPrice(result.lastPrice);
        ref2.ceiling = result.upperLimitPrice;
        ref2.floor = result.lowerLimitPrice;

        if (!instrument) {
            ref2.upperBuyLimit = 0;
            ref2.lowerSellLimit = 0;
        }
        else {
            ref2.upperBuyLimit = Math.min(ref2.ceiling, ref2.upperBuyLimit);
            ref2.lowerSellLimit = Math.max(ref2.floor, ref2.lowerSellLimit);
        }

        ref2.volume = 0;
        this.sendPositionRequest(instrument);

        if (this.isAutoMode()) {
            this.link2Task(instrument);
        }
    }

    sendPositionRequest(instrument, callback) {

        this.log(`send a position request from trading panel, mode = ${this.states.focused}`);
        typeof callback == 'function' && this.positionCallbacks.push(callback);
        this.trigger('request-position', instrument);
    }

    setAsPrice(value) {

        this.log(`set price in trading panel: ${value}`);
        this.mannual.price = this.precisePrice(value) || 0;
    }

    setByRatio(shares) {
        this.handleRatioChange(shares);
    }

    setByCustomRatio(ratio) {
        this.handleRatioChange(100 / ratio);
    }

    handleCustomRatioChange() {

        let ratio = this.mannual.customRatio;
        let defaultv = this.defaults.customRatio;

        if (typeof ratio != 'number' || isNaN(ratio) || ratio < 1) {
            ratio = this.mannual.customRatio = defaultv;
        }

        this.readSetCustomRatio(ratio);
        this.mannual.isEditingCustomRatio = false;
        this.handleRatioChange(100 / ratio);
    }

    handleCustomRatioBlur() {
        this.mannual.isEditingCustomRatio = false;
    }

    handleRatioChange(shares) {

        if (this.isMannualBuy()) {

            if (!this.hasAnyAccount()) {
                return this.interaction.showError('账号信息此刻未获得，请稍后重试');
            }
            
            if (this.isByLimitedPrice() && !(this.mannual.price > 0)) {
                return this.interaction.showError('请输入买入价格，再点击比例');
            }

            if (!this.mannual.instrument) {
                return this.interaction.showError('请输入交易合约');
            }

            /** 总可用资金 */
            let available = this.getAvailableCash(this.mannual.instrument);
            /** 扣除印花税 */
            let tax = available * 1 / 1000;
            /** 可使用净金额 */
            let netAmount = Math.floor(Math.max(0, available - tax) / shares);

            /**
             * 使用金额反算数量
             */
            
            if (this.mannual.isByVolume) {

                let { price, lowerSellLimit, upperBuyLimit, floor, ceiling } = this.mannual;
                let uprice = this.isByLimitedPrice() ? price : this.isMannualBuy() ? Math.min(upperBuyLimit, ceiling) : Math.max(lowerSellLimit, floor);
                let decided = parseInt(netAmount / uprice / 100) * 100;
                this.mannual.volume = Math.max(100, decided);
            }
            else {

                /**
                 * 直接设置金额
                 */
                this.mannual.amount = Math.floor(netAmount);
            }
            
            this.log(`click a cash percentage to buy, manual = ${JSON.stringify(this.mannual)}`);
        }
        else {

            let ref = this.states.linkedPosition;

            if (this.hasLinkedPosition() && ref.instrument == this.mannual.instrument) {
                
                let closable = ref.closableVolume;
                this.mannual.volume = shares == 1 ? closable : closable <= 100 ? closable : parseInt(closable / shares / 100) * 100;
                this.log(`click a position percentage to sell, manual = ${JSON.stringify(this.mannual)}`);
            }
            else {
                this.interaction.showError('仓位信息，不明确，请手动输入数量');
            }
        }
    }

    /**
     * @param {AggregatedPosition} position 
     */
    linkPosition(position) {
        this.states.linkedPosition = position ? this.helper.deepClone(position) : null;
    }

    hasLinkedPosition() {
        return !!this.states.linkedPosition;
    }

    getPositionCondition() {

        var lines = [

            { label: '合约', value: this.mannual.instrumentName || 'N/A' },
            { label: '总仓位', value: 0 },
            { label: '可平仓位', value: 0 },
        ];

        if (this.hasLinkedPosition()) {

            let ref = this.states.linkedPosition;
            lines[0].value = ref.instrumentName;
            lines[1].value = ref.totalPosition.thousands();
            lines[2].value = ref.closableVolume.thousands();
        }

        return lines.map(x => `<span style="line-height:18px;">${x.label}: ${x.value}</span>`).join('<br>');
    }

    /**
     * @param {string} instrument_or_market 
     */
    isShMarket(instrument_or_market) {
        return instrument_or_market && instrument_or_market.toLowerCase().indexOf(this.markets.sh) >= 0;
    }

    /**
     * @param {string} instrument_or_market 
     */
    isSzMarket(instrument_or_market) {
        return instrument_or_market && instrument_or_market.toLowerCase().indexOf(this.markets.sz) >= 0;
    }

    hasAnyAccount() {
        return this.accounts.length > 0;
    }

    /**
     * 拾取归属市场的证券账号
     */
    pickAccount(instrument) {
        
        let sh_act = this.accounts.find(x => x.market.toLowerCase() == this.markets.sh);
        let sz_act = this.accounts.find(x => x.market.toLowerCase() == this.markets.sz);
        return this.isShMarket(instrument) ? sh_act : this.isSzMarket(instrument) ? sz_act : null;
    }

    /**
     * 归属市场的证券账号，是否为信用账号
     */
    isCreditAccount(instrument) {
        
        let act = this.pickAccount(instrument);
        return act && act.credit;
    }

    /**
     * 归属市场的证券账号，可用资金
     */
    getAvailableCash(instrument) {

        let act = this.pickAccount(instrument);
        return act ? act.available : 0;
    }

    isLowBuy1VolumeStrategy() {
        return this.auto.strategy == this.strategyMap.lowControl.value;
    }

    isFixed() {
      return this.auto.strategy == this.strategyMap.fixed.value;
    }

    isIntervally() {
      return this.auto.strategy == this.strategyMap.intervally.value;
    }

    /**
     * @param {AggregatedPosition} position 
     */
    hotUpdatePosition(position) {
        this.linkPosition(position);
    }

    /**
     * @param {AggregatedPosition} position 
     */
    setPositionByRequest(position) {

        this.linkPosition(position);

        while (this.positionCallbacks.length > 0) {

            try {
                this.positionCallbacks.shift()();
            }
            catch(ex) {
                console.error(ex);
            }
        }
    }

    /**
     * @param {AggregatedPosition} position 
     */
    setPositionByExternal(position) {

        /**
         * 当前合约所关联的持仓信息
         */
        this.linkPosition(position);

        /**
         * 设置合约信息
         */
        this.setAsInstrument(position.instrument, position.instrumentName);

        /**
         * 设置除合约以外的，其它输入项
         */

        if (this.isMannualMode()) {

            this.mannual.direction = this.directions.sell;
            this.mannual.isByVolume = true;
        }
        else {

            // todo22
            if (this.isAutoStopped()) {

                this.auto.level = this.vueLevels[0].value;
                this.auto.percent = 1;
            }
        }
    }

    /**
     * @param {TaskObject} task 
     * @returns {TaskObject}
     */
    typeds(task) {
        return task;
    }

    link2Task(instrument) {

        var tsk = this.states.autoTsk = this.typeds(this.tsellsMap[instrument]);
        if (!tsk) {
            return;
        }

        /**
         * 自动卖出交易：如果当前交易面板的合约，有匹配的对应自动卖出策略，则策略的参数，反向写入面板
         */

        var ref = this.auto;
        ref.strategy = tsk.boardStrategy.strategyType;
        ref.percent = tsk.positionPercent || 1;
        ref.level = tsk.priceFollowType;
        ref.volume = tsk.boardStrategy.strategyVolume;
        ref.cancel.enabled = !!tsk.cancelCondition.cancelProtectedEnabled;
        ref.cancel.time = tsk.cancelCondition.cancelProtectedTime;
    }
    
    /**
     * @param {Array<TaskObject>} tasks
     */
    push(tasks) {
        
        tasks.forEach(tsk => { this.tsellsMap[tsk.instrument] = tsk; });
        // 过滤掉不是当前合约的推送
        if (tasks.length == 1) {
            let task = tasks[0];
            if (task.instrument !==  this.auto.instrument) {
                return;
            }
        }
        this.link2Task(this.auto.instrument);
    }

    validateMannualParams() {
        
        var ref = this.mannual;
        var pi = this.priceInfo;
        var lp = this.states.linkedPosition;

        if (!this.hasAnyAccount()) {
            return '没有可用于交易的账号';
        }
        
        if (!ref.instrument) {
            return '合约未指定';
        }
        
        if (this.isByLimitedPrice()) {

            if (!(ref.price > 0)) {
                return '价格，未有效设置';
            }
    
            if (pi && pi.instrument == ref.instrument && !(ref.price >= pi.lowerLimitPrice && ref.price <= pi.upperLimitPrice)) {
                return `价格，超出涨跌停价格：${pi.lowerLimitPrice} ~ ${pi.upperLimitPrice}`;
            }
        }

        let isByAmount = !this.mannual.isByVolume;
        if (isByAmount && this.transferAmount2Volume(ref.price, ref.amount) < 100) {
            return '金额，至少需要能够买入100股';
        }

        let available = this.getAvailableCash(ref.instrument);
        if (isByAmount && ref.amount > available) {
            return `金额 ${ref.amount.thousands()} > 可用现金 ${available.thousands()}`;
        }

        if (this.mannual.isByVolume && !(ref.volume > 0)) {
            return '数量，未有效设置';
        }

        if (this.isMannualSell() && this.hasLinkedPosition() && lp.instrument == ref.instrument && ref.volume > lp.closableVolume) {
            return `数量，超出最大可平数量 = ${lp.closableVolume.thousands()}`;
        }
    }

    mbuy() {
        this.mtrade();
    }

    mcredit() {
        this.mtrade(true);
    }

    msell() {
        this.mtrade();
    }

    mtrade(is_credit_buy = false) {

        let instrument = this.mannual.instrument;
        if (!instrument) {
            return this.interaction.showError('交易合约缺失');
        }

        this.sendPositionRequest(instrument, () => { this.mtradeExec(is_credit_buy); });
    }

    mtradeExec(is_credit_buy = false) {

        var error = this.validateMannualParams();
        if (error) {

            this.interaction.showError(error);
            return;
        }

        var ref = this.mannual;
        var isBuy = ref.direction == this.directions.buy;
        var firstAcnt = this.pickAccount(this.mannual.instrument);
        var cvolume = this.isMannualSell() || this.mannual.isByVolume ? ref.volume : this.transferAmount2Volume(ref.price, ref.amount);
        var direction_text = isBuy ? (is_credit_buy ? '融资买入' : (this.isByLimitedPrice() ? '限价买入' : '市价买入')) : (this.isByLimitedPrice() ? '限价卖出' : '市价卖出');
        var mentions = [

            ['账号', firstAcnt.accountName],
            ['合约', ref.instrument + '/' + ref.instrumentName],
            ['方向', direction_text, isBuy ? 's-color-red' : 's-color-green'],
            this.isByLimitedPrice() ? ['价格', ref.price] : ['价格', (this.marketPriceTypes.find(x => x.value == this.mannual.marketPriceType) || this.marketPriceTypes[0]).label],
            ['数量', cvolume.thousands()],
            ['金额', (ref.price * cvolume).thousands()],
        ];
        
        var message = mentions.map(item => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
        var order = {
        
            accountId: firstAcnt.accountId,
            strategyId: firstAcnt.fundId,
            userId: this.userInfo.userId,
            instrument: ref.instrument,
            volume: cvolume,
            price: ref.price,
            priceType: this.isByLimitedPrice() ? this.systemTrdEnum.pricingType.fixedPrice.code : this.systemTrdEnum.pricingType.marketPrice.code,
            bsFlag: ref.direction,
            businessFlag: is_credit_buy ? this.systemTrdEnum.businessFlag.credit.code : 0,
            positionEffect: 0,
            orderTime: new Date().getTime(),
            hedgeFlag: this.systemTrdEnum.hedgeFlag.Speculate.code,
            customId: '20cm-july-mannual-' + new Date().getTime(),
        };
        
        this.confirm(true, message, () => {

            console.log('to send out an order', order);
            this.placeMannualOrder(order);
            this.interaction.showSuccess('手动订单已发送');
        });
    }

    /**
     * @param {{ instrument: string, volume: number }} order 
     */
    placeMannualOrder(order) {

        let ins = order.instrument;
        let max_volume = 100 * (ins.indexOf('.688') > 0 ? 1000 : ins.indexOf('.3') > 0 ? 3000 : 10000);
        let target_volume = order.volume;
        let waterflow_no = 0;
        
        while (target_volume > 0) {

            let current = Object.assign({}, order);
            current.volume = Math.min(max_volume, target_volume);
            current.customId = `${current.customId}-${waterflow_no++}`;
            target_volume -= current.volume;
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, current);
            this.log(`to send out a manual order: ${JSON.stringify(current)}`);
        }

        setTimeout(() => {

            this.requestAccounts();
            this.sendPositionRequest(this.mannual.instrument); 
        }, 1000);
    }

    isAutoRunning() {

        const ref = this.states.autoTsk;
        return ref && ref.instrument == this.auto.instrument && TaskObject.isTaskRunning(ref.strikeBoardStatus);
    }

    isAutoStopped() {
        return !this.isAutoRunning();
    }

    isAutoCheckedOk() {
        
        var ref = this.auto;
        var message = null;

        if (!ref.instrument) {
            message = '合约未指定';
        }
        else if (this.helper.isNone(ref.strategy)) {
            message = '策略未指定';
        }
        else if (this.helper.isNone(ref.level)) {
            message = '档位未指定';
        }
        else if (this.isLowBuy1VolumeStrategy() && this.helper.isNone(ref.percent)) {
            message = '仓位比例未指定';
        }
        else if (ref.percent < 1) {
            message = '仓位比例为1~100%';
        }

        if (message) {
            this.interaction.showError(message);
        }

        return !message;
    }

    stopAuto() {

        if (this.isAutoStopped()) {
            return;
        }
        
        var ref = this.auto;
        this.confirm(false, `${ref.instrumentName}，停止监控？`, () => {

            this.log(`to stop an auto sell task, task id: ${this.autoTsk.id}, auto: ${JSON.stringify(ref)}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.stop, { id: this.autoTsk.id });
            this.interaction.showSuccess('自动卖出，停止请求已发出');
        });
    }

    isAsNewTask() {

        const ref = this.autoTsk;
        if (!ref) {
            return true;
        }
        else if (this.auto.instrument != ref.instrument) {
            return true;
        }

        const status = ref.strikeBoardStatus;
        return TaskObject.isTaskFinished(status) || TaskObject.isTaskDeleted(status);
    }

    startAuto() {
        
        if (!this.isAutoCheckedOk()) {
            return;
        }
        else if (this.isAutoRunning()) {
            return this.interaction.showError(`${this.auto.instrumentName}，自动卖出，已在执行中`);
        }

        this.confirm(false, `${this.auto.instrument}，开启自动卖出 ?`, () => {

            let ref = this.autoTsk;
            let is_new = this.isAsNewTask();
            this.log(`to start an auto sell task: ${JSON.stringify(ref)}`);
            this.submitAuto(Cm20FunctionCodes.request.start, is_new ? null : ref.id, is_new ? TaskStatus.created.value : ref.strikeBoardStatus);
            this.interaction.showSuccess('自动卖出，启动请求已发出');
        });
    }

    submitAuto(cmdCode, task_id, task_status) {

        var ref = this.auto;
        var zero = 0;
        var isFixed = this.isFixed();
        var isIntervally = this.isIntervally();
        var board_strategy = {

            strategyType: ref.strategy,
            strategyVolumeOpen: false,
            strategyVolume: ref.volume,
            strategyRateOpen: false,
            strategyRate: zero,
            strategyDelayTime: isIntervally ? 3000 : isFixed ? 500 : zero,
            sellAmountOpen: false,
            sellAmount: zero,
            sellVolumeOpen: false,
            sellVolume: zero,
            raiseRateLimit: this.isFixed() ? ref.raiseRateLimit * 10000 : null,
            raiseOffset: this.isFixed() ? ref.raiseOffset * 10000 : null,
        };

        var isLow = this.isLowBuy1VolumeStrategy();
        
        var task = new TaskObject({

            id: task_id,
            userId: this.userInfo.userId,
            userName: this.userInfo.userName,
            instrument: ref.instrument,
            instrumentName: null,
            direction: this.directions.sell,
            priceFollowType: ref.level,
            orderPrice: zero,
            limitPositionType: isLow ? 6 : 1,
            positionPercent: isLow || isFixed ? ref.percent : 1,
            strikeBoardStatus: task_status,
            supplementVolume: zero,
            supplementOpen: false,
            splitInterval: zero,
            splitType: zero,
            splitDetail: { enableMax: false, max: 0, decline: false, enableMost: false, most: 0 },
            boardStrategy: board_strategy,
            cancelCondition: this.isIntervally() ? {
                cancelProtectedEnabled: !!ref.cancel.enabled,
                cancelProtectedTime: ref.cancel.time,
            } : null,
            cash: zero,
            creditFlag: false,
        });

        // console.log('to start a sell task', cmdCode, task);
        this.log(`to submit auto sell task to server: ${cmdCode}/${JSON.stringify(task)}`);
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, cmdCode, task);
    }

    handleParamChange() {

        if (this.isAutoStopped()) {
            return;
        }
        
        var ref = this.autoTsk;
        this.log(`auto sell task param changed: ${JSON.stringify(ref)}`);
        this.submitAuto(Cm20FunctionCodes.request.modify, ref.id, ref.strikeBoardStatus);
        this.interaction.showSuccess('自动卖出，变动已提交');
    }

    handleStrategyChange() {
        this.auto.level = this.vueLevels[0].value
        this.handleParamChange();
    }

    confirm(isRequired, message, callback) {

        if (isRequired) {
            
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => {
                    callback();
                },
            });
        }
        else {
            callback();
        }
    }

    async requestPriceInfo(instrument) {

        var output = { instrument, preClosePrice: 0, lastPrice: 0, upperLimitPrice: 0, lowerLimitPrice: 0 };
        if (!instrument) {
            return output;
        }

        var resp = await repoInstrument.queryPrice(instrument);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            output.preClosePrice = preClosePrice;
            output.lastPrice = lastPrice;
            output.upperLimitPrice = upperLimitPrice;
            output.lowerLimitPrice = lowerLimitPrice;
        }

        return this.priceInfo = output;
    }

    async requestAccounts() {

        if (this.isRequestingAccount) {
            return;
        }

        try {

            this.isRequestingAccount = true;
            var resp = await repoAccount.getAccountDetailInfo({ userId: this.userInfo.userId });
            var { errorCode, errorMsg, data } = resp;
            var records = (data || {}).list || [];
            var accounts = errorCode == 0 ? AccountSimple.Convert(records) : [];
            this.accounts.refill(accounts);

            /**
             * 汇总两市可用资金
             */

            var sh_accounts = accounts.filter(x => this.isShMarket(x.market));
            var sh_available = sh_accounts.map(x => x.available).sum() || 0;
            var sz_accounts = accounts.filter(x => this.isSzMarket(x.market));
            var sz_available = sz_accounts.map(x => x.available).sum() || 0;
            this.states.availableCash = `可用现金：上海 = ${sh_available.thousands()}, 深圳 = ${sz_available.thousands()}`
        }
        catch(ex) {
            console.error(ex);
        }
        finally {
            this.isRequestingAccount = false;
        }
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.requestAccounts();
        setInterval(() => { this.requestAccounts(); }, 1000 * 20);
    }
};