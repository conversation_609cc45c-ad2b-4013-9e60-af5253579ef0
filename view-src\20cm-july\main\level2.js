const { IView } = require('../../../component/iview');
const { HttpTickInfo } = require('../../../model/tick');
const { PriceLevel } = require('../../2021/model/message');
const { SubscribeManager } = require('../subscribe-manager');
const { BizHelper } = require('../../../libs/helper-biz');
const { NumberMixin } = require('../../../mixin/number');
const { DatetimeMixin } = require('../../../mixin/date-time');
const { repoInstrument } = require('../../../repository/instrument');

/**
 * @returns {Array<PriceLevel>}
 */
function MakeLevels(count) {

    var levels = [];

    for (let idx = count; idx >= 1; idx--) {
        levels.push(new PriceLevel(false, '卖' + idx, 0, 0));
    }

    for (let idx = 1; idx <= count; idx++) {
        levels.push(new PriceLevel(true, '买' + idx, 0, 0));
    }

    return levels;
}

module.exports = class Level2View extends IView {

    constructor() {

        super('@20cm-july/main/level2', false, '五档行情');

        /** 档位总数 */
        this.tlevel = 5;
        this.levels = MakeLevels(this.tlevel);
        this.registerEvent('test-instrument', this.testInstrument.bind(this));
        this.submgr = new SubscribeManager(this);
    }

    getLastPrice() {
        return this.states.prices.lastPrice;
    }

    /**
     * 设置为当前合约
     */
    setAsInstrument(instrument, instrumentName) {

        var ref = this.states;
        var last = ref.instrument;

        /**
         * 合约未变更，无需订阅
         */
        if (instrument == last) {
            return;
        }

        this.resetProperties();
        this.resetLevels();
        
        ref.instrument = instrument;
        ref.instrumentName = instrumentName;
        this.subscribeTick(instrument);

        if (instrument) {
            this.requestLimitedPrice(instrument, instrumentName);
        }
    }

    /**
     * 检查某合约，是否为当前行情面板的合约
     * @param {String} instrument
     */
    testInstrument(instrument) {

        if (this.states.instrument != instrument) {
            return;
        }

        this.subscribeTick(null);
        this.resetProperties();
        this.resetLevels();
    }

    resetProperties() {

        var ref = this.states;
        ref.instrument = null;
        ref.instrumentName = null;
        ref.prices.yesterdayClose = null;
        ref.prices.lastPrice = null;
        ref.prices.ceiling = null;
        ref.prices.floor = null;
        ref.increaseRate = null;
        ref.colorClass = null;
    }

    resetLevels() {

        /**
         * 合约产生变化时，首先将档位显示重置
         */
        this.levels.forEach(level => { level.update(0, 0, 0); });
    }

    /**
     * @param {*} current 当前合约
     */
    subscribeTick(current) {

        if (this.tickUpdateJob === undefined) {
            this.tickUpdateJob = setInterval(() => { this.requestTick(); }, 3000);
        }

        this.requestTick();
    }

    async requestTick() {

        if (this.isRequestingTick === true) {
            return;
        }

        this.isRequestingTick = true;
        let instrument = this.states.instrument;
        if (!instrument) {

            this.isRequestingTick = false;
            return;
        }

        try {

            let resp = await repoInstrument.queryTick([instrument]);
            let { errorCode, errorMsg, data } = resp;

            if (errorCode == 0 && Array.isArray(data) && data.length > 0) {
                this.handleTickChange(instrument, data[0]);
            }
        }
        catch(ex) {
            console.error('tick request error', ex);
        }
        finally {
            this.isRequestingTick = false;
        }
    }

    async requestLimitedPrice(instrument, instrumentName) {

        var resp = await repoInstrument.queryPrice(instrument);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};
        var ref = this.states.prices;

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            ref.yesterdayClose = preClosePrice;
            ref.lastPrice = lastPrice;
            ref.ceiling = upperLimitPrice;
            ref.floor = lowerLimitPrice;
            let rate = 100 * (lastPrice - preClosePrice) / (preClosePrice || 99999999);
            this.states.increaseRate = rate;
            this.states.colorClass = rate > 0 ? 's-color-red' : rate < 0 ? 's-color-green' : '';
        }
        else {

            ref.yesterdayClose = 0;
            ref.lastPrice = 0;
            ref.ceiling = 9999;
            ref.floor = 0;
            this.states.increaseRate = null;
            this.states.colorClass = null;
            this.interaction.showError(`${instrumentName}，涨跌停价格未获得：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * @param {HttpTickInfo} tick TICK数据本身
     */
    handleTickChange(instrument, tick) {

        const ref = this.states;
        if (instrument != ref.instrument) {
            return;
        }
        
        this.updateLevels(tick);
        let rate = (tick.lastPrice / tick.preClosePrice - 1) * 100;
        ref.increaseRate = rate;
        ref.colorClass = rate > 0 ? 's-color-red' : rate < 0 ? 's-color-green' : '';
        this.calcPriceCage(tick);
    }

    /**
     * @param {HttpTickInfo} tick 
     */
    calcPriceCage(tick) {

        let lowerSellLimit = 0;
        let upperBuyLimit = 0;
        let { bidPrice, bidVolume, askPrice, askVolume, lastPrice, preClosePrice } = tick;
        let bid_price_1 = bidPrice[0];
        let ask_price_1 = askPrice[0];

        if (bid_price_1 > 0) {
            lowerSellLimit = Math.min(bid_price_1 * 0.98, bid_price_1 - 0.1);
        } 
        else if (ask_price_1 > 0) {
            lowerSellLimit = Math.min(ask_price_1 * 0.98, ask_price_1 - 0.1);
        } 
        else if (lastPrice > 0) {
            lowerSellLimit = Math.min(lastPrice * 0.98, lastPrice - 0.1);
        } 
        else {
            lowerSellLimit = Math.min(preClosePrice * 0.98, preClosePrice - 0.1);
        }

        if (ask_price_1 > 0) {
            upperBuyLimit = Math.max(ask_price_1 * 1.02, ask_price_1 + 0.1);
        } 
        else if (bid_price_1 > 0) {
            upperBuyLimit = Math.max(bid_price_1 * 1.02, bid_price_1 + 0.1);
        } 
        else if (lastPrice > 0) {
            upperBuyLimit = Math.max(lastPrice * 1.02, lastPrice + 0.1);
        } 
        else {
            upperBuyLimit = Math.max(preClosePrice * 1.02, preClosePrice + 0.1);
        }

        this.trigger('cage-price-change', lowerSellLimit, upperBuyLimit);
    }

    /**
     * @param {HttpTickInfo} tick 
     */
    updateLevels(tick) {

        const total = this.tlevel;
        const { askVolume, askPrice, bidVolume, bidPrice, preClosePrice } = tick;

        for (let idx = 0; idx < total; idx++) {
            this.levels[total - 1 - idx].update(askPrice[idx], preClosePrice, Math.ceil(askVolume[idx] * 0.01), askPrice[0]);
        }

        for (let idx = 0; idx < total; idx++) {
            this.levels[total + idx].update(bidPrice[idx], preClosePrice, Math.ceil(bidVolume[idx] * 0.01), bidPrice[0]);
        }
    }

    handleReconnect() {
        this.subscribeTick(this.states.instrument);
    }

    createApp() {

        this.states = {

            instrument: null,
            instrumentName: null,
            increaseRate: null,
            colorClass: '',

            prices: {

                yesterdayClose: null,
                lastPrice: null,
                ceiling: null,
                floor: null,
            },
        };

        new Vue({

            el: this.$container.firstElementChild,
            data: {

                states: this.states,
                levels: this.levels,
            },
            mixins: [NumberMixin, DatetimeMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.simplyHands,
                this.precisePercent,
                this.precisePrice,
                this.setAsPrice,
            ]),
        });
    }

    /**
     * 简化手数显示
     * @param {Number} volume 
     */
    simplyHands(volume) {
        return volume + ' 手';
    }

    precisePercent(price) {
        return typeof price == 'number' ? price.toFixed(2) : price;
    }

    getPricePrecision() {
        
        let instrument = this.states.instrument;
        return BizHelper.getPricePrecision(instrument);
    }

    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(this.getPricePrecision()) : price;
    }

    setAsPrice(price) {
        this.trigger('level-selected', price);
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
    }
};