﻿/*
    global data & context data
*/

const electron = require('electron');
const app = electron.app;
const dataKey = require('./app-data-key').dataKey;
const EventEmitter = require('events').EventEmitter;
const fs = require('fs');
const path = require('path');

// set the max number of listeners listening on a same named channel
EventEmitter.defaultMaxListeners = 200;

/**
 * 服务器管理单元
 */

app.serverManager = null;

/**
 * 交易服务器 & 行情服务器，状态信息
 */

app.serverStates = {

    isTradingServerConnected: false,
    isQuoteServerConnected: false,
};

/**
 * 应用程序上下文数据（字典）
 */

const ContextData = {};
for (let each_key in dataKey) {
    let key_name = dataKey[each_key];
    ContextData[key_name] = null;
}

// app context data
app.contextData = ContextData;

/**
 * app settings
 */
var appSettings = null;

function readSetAppSettings() {

    var app_path = process.cwd();
    var config_file = path.join(app_path, 'settings.js');
    
    if (!fs.existsSync(config_file)) {
        return;
    }

    try {
        appSettings = require(config_file);
    }
    catch (ex) {
        //
    }
}

readSetAppSettings();

/**
 * APP常规设置
 */
const GeneralSettings = {

    /**
     * 软件运行期间，的锁屏时间设置
     * 1. [true], 采用系统默认的20分钟锁屏时间延迟，进行锁屏
     * 2. [false], 系统禁止锁屏
     * 3. [0]，视为[false]处理
     * 4. [5 ~ 720], 系统按照指定的时间延迟，进行锁屏
     * 5. [未指定，或任何其他]，视为[true]处理
     */
    lockScreen: true,

    /**
     * 系统主要的RESTFUL服务，采用HTTP还是HTTPS访问方式，默认为HTTP
     */
    https: false,

    /**
     * 登录形式（ user | account ），默认为 user
     */
    signin: 'user',

    /**
     * 是否为合并市场模式（用于账号详情请求地址的判断）
     */
    combinedMarket: true,
};

/**
 * 加密选项
 */
const encryptionOptions = {
    
    /**
     * 是否对用户登录密码、交易账号密码、终端等密码数据，传输前进行加密
     */
    encryptPasscode: false,

    /**
     * 是否对服务器SOCKET数据包数据加密
     */
    encryptMessage: false,

    /**
     * 是否对系统日志文件加密
     */
    encryptLog: false,

    /**
     * 是否加密授权策略文件
     */
    encryptAuthFile: true,
};

/**
 * 大单高亮显示选项
 */
const bigOrderOptions = {
    
    /** 主板手数 */
    main: 3000,
    /** 其它板块手数 */
    others: 1000,
};

/**
 * 快捷键选项
 */
const shortcutOptions = {
    
    /** 是否启用同花顺快捷键联动功能 */
    enable_ths_shortcut: false,
    /** 从同花顺捕获当前界面的合约 */
    ths_capture: '',
    /** 从同花顺捕获当前界面的合约 */
    ths_capture_2: '',
};

function readLockScreenSettings() {

    var lock_screen = appSettings.lockScreen;
    var result;

    if (typeof lock_screen == 'boolean') {
        result = lock_screen;
    }
    else if (typeof lock_screen == 'number' && lock_screen >= 5 && lock_screen <= 720) {
        result = lock_screen;
    }
    else {
        result = true;
    }

    GeneralSettings.lockScreen = result;
}

function readHttpsSettings() {

    var https = appSettings.https;
    var result;

    if (typeof https == 'boolean') {
        result = https;
    }
    else if (typeof https == 'number' && (https === 0 || https === 1)) {
        result = !!https;
    }
    else {
        result = false;
    }

    GeneralSettings.https = result;
}

function readSigninSettings() {

    var signin = appSettings.signin;
    GeneralSettings.signin = ['user', 'account'].indexOf(signin) >= 0 ? signin : 'user';
}

function readCombindedMarketSettings() {

    var value = appSettings.combinedMarket;
    GeneralSettings.combinedMarket = value == true || value == 1;
}

function readEncryptionSettings() {

    var encryp_opt = appSettings.encryption;
    if (!encryp_opt) {
        return;
    }

    const { passcode, message, log, authfile } = encryp_opt;
    encryptionOptions.encryptPasscode = typeof passcode == 'boolean' ? passcode : false;
    encryptionOptions.encryptMessage = typeof message == 'boolean' ? message : false;
    encryptionOptions.encryptLog = typeof log == 'boolean' ? log : false;
    encryptionOptions.encryptAuthFile = typeof authfile == 'boolean' ? authfile : true;
}

function readBigOrderSettings() {

    var bo_opt = appSettings.bigOrder;
    if (!bo_opt) {
        return;
    }

    var main = bo_opt.main;
    var others = bo_opt.others;

    if (typeof main == 'number' && main >= 1 && main <= ***********) {
        bigOrderOptions.main = main;
    }

    if (typeof others == 'number' && others >= 1 && others <= ***********) {
        bigOrderOptions.others = others;
    }
}

function readShortcutSettings() {
    
    var shortcut_opt = appSettings.shortcut;
    if (!shortcut_opt) {
        return;
    }

    const { enable_ths_shortcut, ths_capture, ths_capture_2 } = shortcut_opt;
    if (typeof enable_ths_shortcut == 'boolean') {
        shortcutOptions.enable_ths_shortcut = enable_ths_shortcut;
    }
    
    if (ths_capture) {
        shortcutOptions.ths_capture = ths_capture;
    }

    if (ths_capture_2) {
        shortcutOptions.ths_capture_2 = ths_capture_2;
    }
}

if (appSettings) {

    readLockScreenSettings();
    readHttpsSettings();
    readSigninSettings();
    // readCombindedMarketSettings();
    readEncryptionSettings();
    readBigOrderSettings();
    readShortcutSettings();
}

/**
 * 常规设置，写入根数据节点
 */
app.GeneralSettings = GeneralSettings;

/**
 * 加密选项，写入根数据节点
 */
app.encryptionOptions = encryptionOptions;

/**
 * 大订单，写入根数据节点
 */
app.bigOrderOptions = bigOrderOptions;

/**
 * 快捷键选项，写入根数据节点
 */
app.shortcutOptions = shortcutOptions;

/**
 * 前序撤单，写入根数据节点
 */
app.frontCancel = appSettings.frontCancel === true;


/**
 * 发布软件版本
 */
var appVersion = null;

var readSetAppVersion = () => {

    var app_path = process.cwd();
    var version_file = path.join(app_path, 'version');
    
    if (!fs.existsSync(version_file)) {
        return;
    }

    try {
        let content = fs.readFileSync(version_file, { encoding: 'utf8' });
        if (content && content.length > 0) {
            appVersion = content.toString().substr(0, 50);
        }
    }
    catch (ex) {
        //
    }
}

readSetAppVersion();
app.contextData.appVersion = appVersion;

/**
 * 硬盘序列号
 */
app.contextData.diskSN = null;

module.exports = {};