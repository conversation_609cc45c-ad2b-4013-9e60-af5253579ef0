<div class="v20cm-july">

	<!-- 按钮绝对定位，固定右上角 -->

	<span class="setting-btn">

		<el-button size="mini" type="primary" @click="sh2szAll">全转深圳</el-button>
		<el-button size="mini" type="danger" @click="sz2shAll">全转上海</el-button>
		<el-button size="mini" type="warning" @click="share" class="s-mgr-10">平分资金</el-button>

		<span class="cash-transfer-box">
			
			<el-select v-model="states.accountId" placeholder="账号" class="ctr-account">
				<el-option v-for="(item, item_idx) in accounts" :key="item_idx" :value="item.shaccount.accountId" :label="item.shaccount.accountName"></el-option>
			</el-select>

			<el-select v-model="states.direction" placeholder="方向" class="ctr-direction s-mgl-10">
				<el-option v-for="(item, item_idx) in directions" :key="item_idx" :value="item.value" :label="item.label"></el-option>
			</el-select>

			<el-select v-model="states.way" placeholder="方式" class="ctr-way s-mgl-10">
				<el-option v-for="(item, item_idx) in ways" :key="item_idx" :value="item.value" :label="item.label"></el-option>
			</el-select>

			<template v-if="isByAmount()">
				<el-input-number 				
					placeholder="划转金额(万元)" 
					class="ctr-cash s-mgl-10"
					:min="0"
					:max="*********" 
					:step="0.01" 
					:precision="2"
					:controls="false"
					v-model="states.amount"
					clearable
				>
				</el-input-number>
				<el-select v-model="states.times" class="ctr-times s-mgl-10" :class="{ highlighted: states.times == timesup.tenk.value }">
					<el-option v-for="(item, item_idx) in timesup" :key="item_idx" :value="item.value" :label="item.label"></el-option>
				</el-select>
			</template>

			<template v-else>
				<el-input-number 
					placeholder="划转比例 1-100" 
					class="ctr-cash s-mgl-10" 
					:min="0"
					:max="100" 
					:step="0.01" 
					:precision="2"
					:controls="false"
					v-model="states.percent" 
					clearable
				>
				</el-input-number>
				<span style="padding: 0 5px;">%</span>
			</template>
		</span>

		<el-button size="mini" type="primary" @click="allocate">划拨资金</el-button>
		
		<el-button size="mini" type="primary" @click="openSetting">
			<i class="el-icon-s-tools"></i>
			<span>交易设置</span>
		</el-button>

		<el-button size="mini" type="primary" @click="openRingtonSetting">
			<i class="el-icon-setting"></i>
			<span>交易铃音设置</span>
		</el-button>

		<el-button size="mini" type="primary" @click="syncAccountData">
			<i class="el-icon-coin"></i>
			<span>数据同步</span>
		</el-button>

	</span>

	<div class="part-left-view">
		<div class="view-task regular-view"></div>
		<div class="view-strategy regular-view"></div>
		<div class="view-queue regular-view"></div>
	</div>

	<div class="part-right-view">
		<div class="view-task-ordered regular-view"></div>
		<div class="view-main"></div>
	</div>

	<div class="footer-row">
		<div class="footer-row-inner">
			<template>
				<div class="summary-item">
					<div class="upper-row">上海</div>
					<div class="lower-row">深圳</div>
					<div class="lower-row">合计</div>
				</div>
				<div class="summary-item">
					<div class="upper-row">总资产 {{ thousandsInt(summary.balance) }}</div>
					<div class="lower-row">总资产 {{ thousandsInt(summarySz.balance) }}</div>
					<div class="lower-row">总资产 {{ thousandsInt(summary.balance + summarySz.balance) }}</div>
				</div>
				<div class="summary-item">
					<div class="upper-row">可用 {{ thousandsInt(summary.available) }}</div>
					<div class="lower-row">可用 {{ thousandsInt(summarySz.available) }}</div>
					<div class="lower-row">可用 {{ thousandsInt(summary.available + summarySz.available) }}</div>
				</div>
				<div class="summary-item">
					<div class="upper-row">可融 {{ thousandsInt(summary.credit) }}</div>
					<div class="lower-row">可融 {{ thousandsInt(summarySz.credit) }}</div>
					<div class="lower-row">可融 {{ thousandsInt(summary.credit + summarySz.credit) }}</div>
				</div>
				<div class="summary-item">
					<div class="upper-row">市值 {{ thousandsInt(summary.equity) }}</div>
					<div class="lower-row">市值 {{ thousandsInt(summarySz.equity) }}</div>
					<div class="lower-row">市值 {{ thousandsInt(summary.equity + summarySz.equity) }}</div>
				</div>
			</template>
		</div>
	</div>

	<div class="setting-view-box"></div>
	<div class="rington-view-box"></div>
	
</div>
