<div class="trade-view-block">
	
	<div class="view-toolbar s-pdl-10 s-pdr-10">

		<span class="view-title">{{ title }}</span>

		<el-autocomplete
			ref="kw"
			placeholder="输入代码或名称"
			v-model="states.keywords"
			:fetch-suggestions="handleSuggest"
			@keydown.native="handleInput"
			@clear="handleClear"
			@select="handleSelectProxy"
			prefix-icon="iconfont icon-sousuo"
			class="s-pdl-10"
			style="width: 170px;"
			clearable>
	
			<template slot-scope="{ item: ins }">
				<span class="item-name">{{ ins.instrumentName }}</span>
				<span class="item-code"> - {{ shortizeCode(ins.instrument) }}</span>
			</template>
	
		</el-autocomplete>

		<span class="s-pull-right" style="display: block;">
			
			<el-tooltip content="刷新任务">
				<a @click="refresh" class="s-mgr-10 el-icon-refresh s-fs-16 s-opacity-7 s-opacity-hover"></a>
			</el-tooltip>

			<el-tooltip content="导出任务">
				<a @click="download" class="s-mgr-10 iconfont icon-upload s-fs-16 s-opacity-7 s-opacity-hover"></a>
			</el-tooltip>

			<el-tooltip content="导出当日策略日志">
				<a @click="downloadTaskLog" class="s-mgr-10 el-icon-download s-fs-16 s-opacity-7 s-opacity-hover"></a>
			</el-tooltip>

		</span>

    <span class="batchs">
      <span @click="batchStart" class="item start">一键启动</span>
      <span @click="batchStop" class="item stop">一键停止</span>
    </span>
	
	</div>

	<!-- 该表格仅用于策略日志的导出，非可见的显示用途 -->

	<div style="height: 0; overflow: auto;">
		<div class="table-4-task-export">
			<table>
				<tr>
					<th label="证券代码" prop="instrument" width="120" formatter="shortizeCodeCol" overflowt></th>
					<th label="证券名称" prop="instrumentName" width="120" overflowt></th>
					<th label="策略类型" prop="strategyName" width="80" overflowt></th>
					<th label="涨跌幅" prop="percent" width="65" align="right" class-maker="formatPercentageClass" percentage by100 overflowt></th>
					<th label="涨停价" prop="ceilingPrice" width="65" align="right" precision="2" overflowt></th>
					<th label="状态" prop="status" width="60" formatter="formatTaskStatusHtml" export-formatter="formatTaskStatus" overflowt></th>
					<th label="启停" prop="status" fixed-width="50" formatter="formatRunStop" exportable="false"></th>
					<th label="操作" prop="status" fixed-width="50" formatter="formatOper" exportable="false"></th>
				</tr>
			</table>			
		</div>
		<div class="table-4-log-export">
			<table>
				<tr>
					<th label="ID" prop="id" formatter="render2String"></th>
					<th label="日期" prop="tradingDay"></th>
					<th label="策略ID" prop="strikeId" formatter="render2String"></th>
					<th label="合约代码" prop="instrument"></th>
					<th label="合约名称" prop="instrumentName"></th>
					<th label="自动策略ID" prop="taskId" formatter="render2String"></th>
					<th label="票池" prop="ticketPoolName"></th>
					<th label="设置" prop="autoStrikeBoardSettingName"></th>
					<th label="类型" prop="type" formatter="renderType"></th>
					<th label="剔除原因" prop="reason"></th>
					<th label="下单时间" prop="orderTime" formatter="render2String"></th>
					<th label="下单参数" prop="orderParam"></th>
					<th label="下单回报用时(毫秒)" prop="orderUseTime"></th>
					<th label="撤单时间" prop="cancelTime" formatter="render2String"></th>
					<th label="撤单参数" prop="cancelParam"></th>
					<th label="补单时间" prop="supplementTime" formatter="render2String"></th>
					<th label="补单参数" prop="supplementPram"></th>
				</tr>
			</table>
		</div>
	</div>

	<div class="tables2 s-full-height">

		<div class="table-task">

			<table>
				<tr>
					<th label="证券代码" prop="instrument" width="120" formatter="shortizeCodeCol" sortable overflowt></th>
					<th label="证券名称" prop="instrumentName" width="120" sortable overflowt></th>
					<th label="策略类型" prop="strategyName" width="80" overflowt></th>
					<th label="涨跌幅" prop="percent" width="65" align="right" class-maker="formatPercentageClass" percentage by100 sortable overflowt></th>
					<th label="涨停价" prop="ceilingPrice" width="65" align="right" precision="2" overflowt></th>
					<th label="状态" prop="status" width="60" formatter="formatTaskStatusHtml" export-formatter="formatTaskStatus" sortable overflowt></th>
					<th label="启停" prop="status" fixed-width="50" formatter="formatRunStop" exportable="false"></th>
					<th label="操作" prop="status" fixed-width="50" formatter="formatOper" exportable="false"></th>
				</tr>
			</table>
			
		</div>

		<div class="splitter-line"></div>

		<div class="table-task-2-ext">
			<div class="table-task-2 s-full-height">
				<table>
					<tr>
						<th label="证券代码" prop="instrument" width="120" formatter="shortizeCodeCol" sortable overflowt></th>
						<th label="证券名称" prop="instrumentName" width="120" sortable overflowt></th>
						<th label="策略类型" prop="strategyName" width="80" overflowt></th>
						<th label="涨跌幅" prop="percent" width="65" align="right" class-maker="formatPercentageClass" percentage by100 sortable overflowt></th>
						<th label="涨停价" prop="ceilingPrice" width="65" align="right" precision="2" overflowt></th>
						<th label="状态" prop="status" width="60" formatter="formatTaskStatusHtml" export-formatter="formatTaskStatus" sortable overflowt></th>
						<th label="启停" prop="status" fixed-width="50" formatter="formatRunStop" exportable="false"></th>
						<th label="操作" prop="status" fixed-width="50" formatter="formatOper" exportable="false"></th>
					</tr>
				</table>
			</div>
		</div>

	</div>
	
</div>