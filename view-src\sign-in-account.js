const IView = require('../component/iview').IView;
const path = require('path');
const fs = require('fs');
const IpLib = require('ip');
const svgCaptcha = require('../libs/3rd/svg-captcha');
const { app } = require('@electron/remote');
const { encryptAuthFile } = app.encryptionOptions;
const { isDev } = require('../config/environment');
const { repoApplication } = require('../repository/application');
const { LocalFileStorage } = require('../toolset/local-file-storage');
const { encodeString } = require('./encoder');

class InputItem {

    /**
     * @param {String} label 
     * @param {String} field 
     * @param {String} value 
     */
    constructor(label, field, value, options = { id: null, ctype: null, clsname: null, isBranch: false, isServer: false, isCaptcha: false, }) {

        this.label = label;
        this.field = field;
        this.value = value || '';
        this.error = null;
        this.disabled = false;

        if (options.id) {
            this.id = options.id;
        }

        if (options.ctype) {
            this.ctype = options.ctype;
        }

        if (options.clsname) {
            this.clsname = options.clsname;
        }

        if (options.isBranch) {
            this.isBranch = !!options.isBranch;
        }

        if (options.isServer) {
            this.isServer = !!options.isServer;
        }

        if (options.isCaptcha) {
            this.isCaptcha = !!options.isCaptcha;
        }
    }
}

/**
 * @returns {Array<{ id, orgId, branchId, name }>}
 */
function allocateBranchs() {
    return [];
}

/**
 * @returns {Array<{ id, name, tradingServer, quoteServer, quoteRestfulServer, restfulServer, restfulServerSz, historyServer, indayServer }>}
 */
function allocateServers() {
    return [];
}

class View extends IView {

    get macLib() {
        return this._macLib || (this._macLib = require('getmac'));
    }

    get logR() {
        return this.systemUserEnum.loginResult;
    }

    get isHttps() {

        try {
            return this.app.GeneralSettings.https;
        }
        catch(ex) {
            return false;
        }
    }

    get isCombinedMarket() {

        try {
            return this.app.GeneralSettings.combinedMarket;
        }
        catch(ex) {
            return false;
        }
    }

    get $passcode() {
        return document.getElementById('input-user-passcode');
    }

    get $signBtn() {
        return document.getElementById('btn-to-sign-in');
    }

    get $captcha() {
        return document.getElementById('captcha-img');
    }

    constructor(view_name) {

        super(view_name, true, '用户登录');

        this.storageKeys = {

            remember: 'gtsec_remember_user',
            recentLogin: 'gtsec_recent_user',
            serverInfo: 'serverInfo',
        };

        this.branchs = allocateBranchs();
        this.servers = allocateServers();
        this.states = {

            trdPassed: null,
            quotePassed: null,
        };

        this.ctrs = {

            branch: new InputItem('营业部门', 'branch', null, { isBranch: true }),
            account: new InputItem('资金账号', 'tradeAccount', null, { clsname: 'input-sec-account' }),
            server: new InputItem('服务器', 'server', null, { isServer: true }),
            passcode: new InputItem('登录密码', 'passcode', null, { id: 'input-user-passcode', ctype: 'password' }),
            captcha: new InputItem('验证码', 'captcha', null, { id: 'input-captcha', clsname: 'input-item-captcha', isCaptcha: true }),
        };

        this.uidata = {

            credit: false,
            rememberUser: LocalFileStorage.getItem(this.storageKeys.remember) == 1,
            isSigningIn: false,
            /** 是否为本地加密密码 */
            isLocalEncryptedPwd: null,
        };

        this.recoverRecent();
    }

    recoverRecent() {

        if (!this.uidata.rememberUser) {
            return;
        }

        var lastData = this.getRecentLogInfo();
        if (!lastData) {
            return;
        }

        var { account, server, is_credit } = lastData;
        var ctrs = this.ctrs;

        ctrs.account.value = account;
        ctrs.server.value = server;
        this.uidata.credit = !!is_credit;
    }

    /**
     * @returns {{ branch, account, server, is_credit }}
     */
    getRecentLogInfo() {
        return LocalFileStorage.getItem(this.storageKeys.recentLogin);
    }

    setRecentLogInfo() {

        var ctrs = this.ctrs;

        if (this.helper.isNone(ctrs.account.value)) {

            LocalFileStorage.removeItem(this.storageKeys.recentLogin);
            return;
        }
        
        LocalFileStorage.setItem(this.storageKeys.recentLogin, {

            branch: ctrs.branch.value,
            account: ctrs.account.value.trim(),
            server: ctrs.server.value,
            is_credit: !!this.uidata.credit,
        });
    }

    handleRembChange() {
        LocalFileStorage.setItem(this.storageKeys.remember, this.uidata.rememberUser ? 1 : 0);
    }

    checkServer() {

        this.checkInput(this.ctrs.server);
        this.bindBranchs();
    }

    /**
     * @param {InputItem} control
     */
    checkInput(control) {

        control.error = this.helper.isNotNone(control.value) ? null : `${control.label}，未输入`;
        var theCap = this.ctrs.captcha;

        if (!control.error && control === theCap) {

            let isSame = control.value.toLowerCase() == this.validation.text.toLowerCase();
            control.error = isSame ? null : '验证码错误';
        }
    }

    createApp($container) {
        
        var ctrs = this.ctrs;
        var ctrlist = [ctrs.branch, ctrs.account, ctrs.server, ctrs.passcode, ctrs.captcha];
        this.ctrlist = ctrlist;

        this.vueApp = new Vue({
            
            el: $container,

            data: {

                ctrs: ctrlist,
                branchs: this.branchs,
                servers: this.servers,
                uidata: this.uidata,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.closeWindow,
                this.checkInput,
                this.checkServer,
                this.check2SignIn,
                this.finishInput,
                this.refreshCaptcha,
                this.handleRembChange,
            ]),
        });

        this.vueApp.$nextTick(() => {
            this.refreshCaptcha();
        });
    }

    finishInput(isLast) {

        var { target, keyCode } = event;
        if (keyCode != 13) {
            return;
        }

        if (isLast) {

            this.$signBtn.click();
            return;
        }

        var $source = target;
        var clsname = 'input-item';

        while (!$source.classList.contains(clsname)) {
            $source = $source.parentElement;
        }

        var $next = $source.nextElementSibling;
        if (!$next.classList.contains(clsname)) {
            return;
        }

        var $input = $source.querySelector('input');
        if ($input) {
            $input.focus();
        }
    }

    closeWindow() {
        this.thisWindow.close();
    }

    refreshCaptcha() {

        this.validation = svgCaptcha.create({

            size: 4, // 验证码长度为 4 个字符
            ignoreChars: '0o1iIlL', // 忽略容易混淆的字符
            noise: 0, // 添加 0 条噪声线
            color: true, // 启用彩色字符
            background: '#ffffff', // 设置较淡的背景色
            width: 150, // 验证码图片宽度
            height: 50, // 验证码图片高度
            fontSize: 50, // 字体大小
            charPreset: 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnopqrstuvwxyz23456789' // 自定义字符集（排除容易混淆的字符）
        });

        this.$captcha.innerHTML = this.validation.data;
    }

    async getDiskSN() {

        if (this._diskSN !== undefined) {
            return this._diskSN;
        }
        
        try {

            let si = require('systeminformation');
            let results = await si.diskLayout();
            this._diskSN = results.length > 0 ? results[0].serialNum : 'NA';
        }
        catch(ex) {
            this._diskSN = 'NA';
        }
        finally {
            return this._diskSN;
        }
    }

    async getPCName() {

        if (this._pcname !== undefined) {
            return this._pcname;
        }
        
        try {

            let si = require('systeminformation');
            let results = await si.osInfo();
            this._pcname = results.hostname || 'NA';
        }
        catch(ex) {
            this._pcname = 'NA';
        }
        finally {
            return this._pcname;
        }
    }

    async getCpuProcessorId() {

        if (this._processorId !== undefined) {
            return this._processorId;
        }
        
        try {

            let cp = require('child_process');
            cp.exec('wmic CPU get ProcessorID', null, (err, stdout, stderr) => {

                try {

                    if (err || stderr) {
                        this._processorId = 'NA';
                    }
                    else {
                        this._processorId = stdout.toString().split('\n')[1].trim();
                    }
                }
                catch(ex) {
                    //
                }
            });
        }
        catch(ex) {
            this._processorId = 'NA';
        }
        finally {
            return this._processorId;
        }
    }

    /**
     * 对所有控件值，进行验证
     */
    hasAnyError() {

        this.ctrlist.forEach(ctr => { this.checkInput(ctr); });
        return this.ctrlist.some(ctr => !!ctr.error);
    }

    check2SignIn() {

        var ctrs = this.ctrs;

        if (isDev) {
            ctrs.captcha.value = this.validation.text;
        }

        if (this.hasAnyError()) {

            this.interaction.showError('请根据提示，进行填写');
            return;
        }

        /**
         * 记忆当前登录主要输入内容（排除密码）
         */
        if (this.uidata.rememberUser) {
            this.setRecentLogInfo();
        }

        this.refreshCaptcha();

        this.macLib.getMac(async (err, macAddr) => {

            let platform = require('os').platform();
            let hdSn = await this.getDiskSN();
            let pcname = await this.getPCName();
            let ipAddr = IpLib.address();
            let processor = this._processorId || 'NA';
            this.doSignIn({ platform, processor, pcname, hdSn, ipAddr, macAddr: err ? '' : macAddr.toUpperCase() });
        });
    }

    getStrategyConfig(signin_token) {

        let app_path = process.cwd();
        let authentic_file_1 = path.join(app_path, signin_token);
        let authentic_file_2 = path.join(app_path, 'auth-files', signin_token);
        let has_file_1 = fs.existsSync(authentic_file_1);
        let has_file_2 = fs.existsSync(authentic_file_2);
        let absence = '';

        if (!has_file_1 && !has_file_2) {
            return { raw: absence, encrypted: absence };
        }

        try {

            let authentic_file = has_file_1 ? authentic_file_1 : authentic_file_2;
            let content = fs.readFileSync(authentic_file, { encoding: 'utf8' });
            let has_config = typeof content == 'string' && content.length > 0;
            if (has_config && encryptAuthFile) {
                content = this.helper.ununicode(this.helper.aesDecrypt(content));
            }
            let converted = has_config ? this.helper.tomd5(signin_token + content) : absence;
            return {
                raw: has_config ? content : absence,
                encrypted: has_config ? converted : absence,
            };
        }
        catch (ex) {
            return { raw: absence, encrypted: absence };
        }
    }
    
    doSignIn({ platform, processor, pcname, hdSn, ipAddr, macAddr }) {

        var { account, passcode } = this.ctrs;
        var loginTypes = { byUser: 0, byAccount: 1 };
        var branch = this.branchs.find(x => x.id == this.ctrs.branch.value);
        var sconfig = this.getStrategyConfig((account.value || '').trim());
        var loginData = {

            loginType: loginTypes.byAccount,
            userName: account.value,
            orgId: branch.orgId,
            branchId: branch.branchId,
            credit: this.uidata.credit ? 1 : 0,
            passCode: this.uidata.isLocalEncryptedPwd ? passcode.value.trim() : encodeString(passcode.value.trim()),
            os: `${platform}|${hdSn}|${ipAddr}|${pcname}|${processor}`,
            macAddr,
            ipAddr,
            md5: this.helper.getVersion(isDev),
            configStr: sconfig.encrypted,
            rawConfigStr: sconfig.raw,
        };
        
        if (!loginData.macAddr) {
            this.loggerSys.error('MAC addr not fetched');
        }

        var server = this.servers.find(x => x.id == this.ctrs.server.value);
        var cloned_input = Object.assign({}, loginData);
        delete cloned_input.passCode;
        this.resetFlags(loginData, hdSn, server);
        this.loggerSys.debug(`sign-in window > to login to server: ${JSON.stringify(server)} with input: ${JSON.stringify(cloned_input)}`);
        // only login successfully onto [trading] server, and then can quote server tries to login <if required depending on user role>
        this.renderProcess.send(this.systemEvent.toLoginTradingServer);
    }

    resetFlags(loginData, hdSn, server) {

        this.states.trdPassed = null;
        this.states.quotePassed = null;
        this.uidata.isSigningIn = true;

        this.app.contextData.disconnectedByLogout = null;
        this.app.contextData.disconnectedTradingServerAccidently = null;
        this.app.contextData.disconnectedQuoteServerAccidently = null;

        this.app.contextData.logInInput = loginData;
        this.app.contextData.diskSN = hdSn;

        /**
         * 当前登录选择的服务器，写入本地缓存（用于欢迎窗口） + 和APP上下文内存对象
         */

        LocalFileStorage.setItem(this.storageKeys.serverInfo, server);
        this.app.contextData.serverInfo = server;
    }

    quoteServerNotRequired(signin_response) {
        // return signin_response.roleId === this.systemUserEnum.userRole.superAdmin.code;
        return true;
    }

    listen2TradingServerLoginCompletement() {

        this.renderProcess.on(this.systemEvent.loginTradingServerCompleted, (event, signin_response) => {

            this.states.trdPassed = signin_response.errorCode === this.logR.ok.code;

            if (!this.states.trdPassed) {
                this.uidata.isSigningIn = false;
            }

            // only when login onto trading server is validated ok, then can try to login onto quote server (if required for this user type)
            if (this.states.trdPassed && this.quoteServerNotRequired(signin_response)) {

                this.loggerSys.debug(`sign-in window > user with username = ${signin_response.userName}, fullname = ${signin_response.fullName} does not require quote server`);
                // bypass login onto quote server
                this.states.quotePassed = true;
                this.handleSignInResponse(signin_response, '交易服务器');
                return;
            }

            /*
			    set trading server login feedback,
			    no matter [ok] or [error]
			    and it will be 100% replaced with latest quote server login feeback very quickly (if with error).
			*/
            this.handleSignInResponse(signin_response, '交易服务器');

            if (this.states.trdPassed) {
                this.renderProcess.send(this.systemEvent.toLoginQuoteServer);
            }
        });
    }

    listen2QuoteServerLoginCompletement() {

        this.renderProcess.on(this.systemEvent.loginQuoteServerCompleted, (event, signin_response) => {
            this.states.quotePassed = signin_response.errorCode === this.logR.ok.code;
            this.handleSignInResponse(signin_response, '行情服务器');
        });
    }

    notifyFailure(message) {
        this.interaction.showError(message);
    }

    listen2NetworkEvents() {

        /*
		    the followed 3 methods will be fired by [trading] server or [quote] server for aligned event
		    no matter which connection happens to be [FAILED],
            the whole login process is treated as [FAILED]
		*/

        this.renderProcess.on(this.systemEvent.connTimedOut, (event, error_msg) => { this.notifyFailure(error_msg); });
        this.renderProcess.on(this.systemEvent.connError, (event, error_msg) => { this.notifyFailure(error_msg); });
        this.renderProcess.on(this.systemEvent.connClosed, (event, error_msg) => { this.notifyFailure(error_msg); });
    }

    listen2AdminKicksMeOut() {
        this.renderProcess.on(this.serverEvent.forcedKickOut, () => { alert('您已被管理员强制下线'); });
    }

    handleSignInResponse({ errorCode, errorMsg }, server_type_name) {

        if (!this.thisWindow.isVisible()) {
            return;
        }

        var { trdPassed, quotePassed } = this.states;
        var ctrs = this.ctrs;

        /**
         * 交易 & 行情，均已完成登录
         */
        if (trdPassed != null && quotePassed != null) {
            this.uidata.isSigningIn = false;
        }

        /**
         * 交易 & 行情，均登录成功
         */
        if (trdPassed === true && quotePassed === true) {

            this.renderProcess.send(this.systemEvent.loginRequestValidatedOk);
            return;
        }

        /**
         * 交易服务器，或行情服务器，其中一方已经完成，另外一方登录尚未返回
         */

        if (errorCode === this.logR.ok.code) {
            return;
        }

        var logR = this.logR;
        var feedback;

        switch (errorCode) {

            case logR.userNameOrPasscodeError.code: feedback = logR.userNameOrPasscodeError.mean; break;
            case logR.userNameNonExist.code: feedback = logR.userNameNonExist.mean; break;
            case logR.passcodeError.code: feedback = logR.passcodeError.mean; break;
            case logR.userDisabled.code: feedback = logR.userDisabled.mean; break;
            case logR.alreadySignedIn.code: feedback = logR.alreadySignedIn.mean; break;
            case logR.brokerExpception.code: feedback = errorMsg; break;
            default: feedback = errorMsg || '未知错误'; break;
        }

        if (feedback) {
            this.interaction.showError(`(${server_type_name})${feedback}`);
        }
    }

    toExitApp() {

        this.renderProcess.send(this.systemEvent.exitApp);
        this.thisWindow.close();
    }

    bindServers() {

        try {

            var app_path = process.cwd();
            var config_file = path.join(app_path, 'servers');

            if (!fs.existsSync(config_file)) {

                alert('服务器配置文件缺失，请确认：' + config_file);
                this.toExitApp();
                return;
            }

            var server_cfgs = [];
            try {

                const list = require(config_file);
                if (list instanceof Array && list.length > 0) {
                    server_cfgs.merge(list);
                }
            }
            catch (ex) {

                alert('导入服务器配置产生异常，终止运行。');
                this.toExitApp();
                return;
            }

            if (server_cfgs.length == 0) {

                alert('服务器配置，未指定任何可用服务器列表，终止运行。');
                this.toExitApp();
                return;
            }
            else {

                /**
                 * 合并沪深市场的接口时，将深圳强制设置为沪市相同（无论是否有设置深圳）
                 */

                if (this.isCombinedMarket) {

                    server_cfgs.forEach(item => {
                        item.servers.restServerSz = item.servers.restServer;
                    });
                }

                let invalid = server_cfgs.find(x => {

                    let server_name = x.serverName;
                    if (typeof server_name != 'string' || server_name.trim().length == 0) {
                        return true;
                    }

                    let servers = x.servers;
                    return typeof servers.dataServer != 'string' || servers.dataServer.indexOf(':') <= 0
                        || typeof servers.quoteServer != 'string' || servers.quoteServer.indexOf(':') <= 0
                        || typeof servers.quoteRestfulServer != 'string' || servers.quoteRestfulServer.indexOf(':') <= 0
                        || typeof servers.restServer != 'string' || servers.restServer.indexOf(':') <= 0
                        || typeof servers.restServerSz != 'string' || servers.restServerSz.indexOf(':') <= 0
                        || typeof servers.tradeServer != 'string' || servers.tradeServer.indexOf(':') <= 0;
                });

                if (invalid) {

                    alert('服务器配置，非预期数据结构，终止运行。');
                    this.toExitApp();
                    return;
                }
            }

            let prefix = this.isHttps ? 'https://' : 'http://';
            let counter = 1;
            let dto_servers = server_cfgs.map(cfg => {

                let server_name = cfg.serverName;
                let servers = cfg.servers;
                let trade_server = servers.tradeServer.split(':');
                let quote_server = servers.quoteServer.split(':');

                return {

                    id: ++counter,
                    name: server_name,
                    tradingServer: { ip: trade_server[0], port: trade_server[1] },
                    quoteServer: { ip: quote_server[0], port: quote_server[1] },
                    quoteRestfulServer: `http://${ servers.quoteRestfulServer }`,
                    restfulServer: `${ prefix }${ servers.restServer }/quant/v3`,
                    restfulServerSz: `${ prefix }${ servers.restServerSz }/quant/v3`,
                    historyServer: `${ prefix }${ servers.dataServer }/quote/v3`,
                    indayServer: 'http://local.gaoyusoft.com:8181',
                };
            });

            this.servers.refill(dto_servers);
        }
        catch (e) {
            this.interaction.showError('获取服务器列表异常!');
        }
    }

    async bindBranchs() {

        if (this.servers.length == 0) {

            this.ctrs.branch.value = null;
            return;
        }

        var server = this.servers.find(x => x.id == this.ctrs.server.value) || this.servers[0];
        var resp = await repoApplication.qbranchs(server.restfulServer);
        var content = resp.data;
        var lines = typeof content == 'string' ? content.split('\n') : [];
        var branchs = lines.map((ln, ln_idx) => {

            let mebs = ln.trim().replace(/\s{1,}/g, '\t').split('\t');
            return { id: ln_idx, orgId: mebs[0], branchId: mebs[1], name: mebs[2] };
        });
        
        var lastData = this.getRecentLogInfo();
        if (lastData) {
            this.ctrs.branch.value = lastData.branch;
        }

        if (!branchs.some(x => x.id == this.ctrs.branch.value)) {
            this.ctrs.branch.value = branchs.length > 0 ? branchs[0].id : null;
        }

        if (branchs.length > 0) {
            this.branchs.refill(branchs);
        }
    }

    bindLocalPwd() {
        
        var app_path = process.cwd();
        var pwd_file = path.join(app_path, 'encrypted-user-password.txt');

        if (fs.existsSync(pwd_file)) {
            
            let content = fs.readFileSync(pwd_file, { encoding: 'utf8' });
            let has_config = typeof content == 'string' && content.length > 0;
            if (has_config) {

                this.uidata.isLocalEncryptedPwd = true;
                this.ctrs.passcode.value = content.trim();
                this.ctrs.passcode.disabled = true;
            }
        }
    }

    build($container) {

        this.listen2TradingServerLoginCompletement();
        this.listen2QuoteServerLoginCompletement();
        this.listen2NetworkEvents();
        this.listen2AdminKicksMeOut();
        this.bindServers();
        this.bindBranchs();
        this.bindLocalPwd();
        this.createApp($container);
        this.getDiskSN();
        this.getPCName();
        this.getCpuProcessorId();
    }
}

module.exports = View;