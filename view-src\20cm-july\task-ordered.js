const { BaseView } = require('./base-view');
const { SmartTable } = require('../../libs/table/smart-table');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { BuyTask, ZtStandardStrategy, FromTable, RowBehaviors, ZtSetting } = require('./objects');
const { BoughtOrderFrontOrder } = require('../../model/front-order');
const { repoOrder } = require('../../repository/order');
const { BizHelper } = require('../../libs/helper-biz');

module.exports = class OrderedTaskView extends BaseView {

    constructor() {
        
        super('@20cm-july/task-ordered', false, '已买监控');
        this.ztsetting = ZtSetting.makeDefault();
    }

    /**
     * @returns {BuyTask}
     */
    typeds(data) {
        return data;
    }
    
    /**
     * @param {BuyTask} record 
     */
    identify(record) {
        return record.id;
    }

    createTable() {

        const $table = this.$container.querySelector('table');
        const ref = new SmartTable($table, this.identify, this, {

            tableName: 'smt-20cm-task-ordered',
            displayName: this.title,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            rowSelected: this.handleRowSelect.bind(this),
            rowDbClicked: this.handleRowDbClick.bind(this),
        });

        ref.setPageSize(99999);
        return ref;
    }

    /**
     * @param {BuyTask} task 
     */
    handleRowSelect(task) {

        this.log(`user selected an ordered task: ${JSON.stringify(task)}`);
        this.tolerate(FromTable.ordered, RowBehaviors.select, task);
    }

    /**
     * @param {BuyTask} task 
     */
    handleRowDbClick(task) {

        if (!this.ztsetting.doubleClick2Cancel) {
            return;
        }

        this.log(`user double clicked an ordered task: ${JSON.stringify(task)}`);
        this.cancel(task);
    }

    tolerate(from, behavior, task) {
        this.trigger('tolerate', from, behavior, task);
    }

    sortableRows() {

        var ref = $(this.tableObj.$bodyTable.querySelector('tbody'));
        ref.sortable();
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.trigger('refresh-task');
    }

    download() {
        this.tableObj.exportAllRecords();
    }

    highlightCol() {
        return 's-color-red';
    }

    /**
     * @param {BuyTask} task 
     */
    cancel(task) {
        
        this.log(`to cancel an ordered task, task: ${JSON.stringify(task)}`);
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: task.id });
        this.interaction.showSuccess('撤单请求已提交');
    }

    /**
     * @param {BuyTask} task 
     */
    outcancel(task) {

        if (!this.tableObj.hasRow(this.identify(task))) {
            return this.interaction.showError(`未匹配到可撤监控：${task.instrumentName}，请尝试从列表操作！`);
        }

        this.cancel(task);
    }

    /**
     * @param {BuyTask} task 
     */
    formatOper(task) {
        return '<button event.onclick="cancel">撤单</a>';
    }
    
    /**
     * @param {Array<BuyTask>} tasks
     */
    push(tasks) {

        tasks.forEach(item => {

            let row_key = item.id;

            if (!this.isTaskOrdered(item.status) || item.hasCanceled) {

                let is_selected = this.tableObj.isRowSelected(row_key);

                /**
                 * 有对应任务，则删除，并向外层通知
                 */

                if (this.tableObj.hasRow(row_key)) {

                    this.tableObj.deleteRow(row_key);
                    is_selected && this.tolerate(FromTable.ordered, RowBehaviors.delete, item);

                    if (item.hasCanceled) {
                        this.ring4Canceled(item);
                    }
                }
            }
            else {

                let is_new = !this.tableObj.hasRow(row_key);
                this.tableObj.putRow(item);
                is_new && this.ring4TaskBeenMonitored(item);
            }
        });

        if (this.hasInitializedFrontOrder == undefined) {

            this.hasInitializedFrontOrder = true;
            this.updateFrontOrder();
        }
    }

    /**
     * @param {BuyTask} task 来源监控任务
     * @param {ZtStandardStrategy} strategy 
     */
    outupdate(task, strategy) {

        let row_key = this.identify(task);
        let expected = this.typeds(this.tableObj.getRowData(row_key));
        if (!expected) {
            return;
        }

        this.tableObj.updateRow({

            id: expected.id,
            localId: expected.localId,
            strategyName: strategy.name,
            strategy: strategy,
        });

        this.log(`update an ordered task strategy from outside: task id/${task.id}, strategy/${JSON.stringify(strategy)}`);
        this.toSubmit(expected, Cm20FunctionCodes.request.modify, true);
    }

    createToolbarApp() {
        
        const vappIns = new Vue({

            el: this.$container.querySelector('.view-toolbar'),
            data: {
                //
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.cancelChecks,
            ]),
        });
    }

    cancelChecks() {

        if (!this.tableObj.hasAnyRowsChecked) {
            return this.interaction.showError('未有勾选的监控');
        }

        var checkes = this.tableObj.extractCheckedRecords().map(x => this.typeds(x));
        this.log(`to cancel checked tasks: ${JSON.stringify(checkes.map(x => ({ instrument: x.instrument, id: x.id })))}`);
        console.log({ checkes, stocks: checkes.map(x => x.instrument) });
        checkes.forEach(x => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: x.id });
        });
        this.interaction.showSuccess('撤单请求已提交，数量 = ' + checkes.length);
    }

    /**
     * @param {ZtSetting} setting 
     */
    setAsSetting(setting) {
        Object.assign(this.ztsetting, this.helper.deepClone(setting));
    }

    /**
     * @returns {Array<BuyTask>}
     */
    get allTasks() {
        return this.tableObj.extractAllRecords();
    }

    async updateFrontOrder() {

        if (this.isProcessing) {
            return;
        }
        else if (this.tableObj.rowCount == 0) {
            return;
        }

        try {

            let qconditions = this.allTasks.map(item => {
                let { id, instrument, orderNo } = item;
                return { strategyId: id, instrument, orderNo };
            });

            this.isProcessing = true;
            let resp = await repoOrder.queryFrontOrderSummary(qconditions);
            let { errorCode, errorMsg, data } = resp;

            if (errorCode == 0 && this.helper.isJson(data)) {

                let fronts = BoughtOrderFrontOrder.convert(data);
                fronts.forEach(item => {

                    /**
                     * 对匹配的数据行进行更新
                     */
                    if (this.tableObj.hasRow(item.orderId)) {
                        let row = this.tableObj.getRowData(item.orderId);
                        this.tableObj.updateRow({ 
                            id: item.orderId,
                            frontOrder: item.frontOrder / 100,
                            frontOrderAmount: (item.frontOrder * BizHelper.pick(row.instrument).upperLimitPrice / 10000),
                            limitBuy: item.limitBuy / 100,
                            limitSize: item.limitSize,
                        });
                    }
                    else {
                        //
                    }
                });
            }
            else {
                console.error(resp);
            }
        }
        catch (ex) {
            //
        }
        finally {
            this.isProcessing = false;
        }
    }

    build($container) {

        super.build($container);
        this.sortableRows();
        this.createToolbarApp();
        setInterval(() => { this.updateFrontOrder(); }, 1000 * 3);
    }
};