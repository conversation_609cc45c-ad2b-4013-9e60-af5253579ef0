const fs = require('fs');
const { IView } = require('../../component/iview');
const { <PERSON><PERSON><PERSON><PERSON><PERSON> } = require('../../libs/helper-biz');
const { SmartTable } = require('../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../libs/table/column-common-func');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { Entrance, TaskStatus, TaskStatuses, TaskObject, BuyTask, ZtStandardStrategy } = require('./objects');
const { AccountSimple } = require('../20cm/components/objects');

var RingtonSample = {

    entrusted: null,
    bought: null,
    canceled: null,
    sold: null,

    customized: {

        entrusted: null,
        bought: null,
        canceled: null,
        sold: null,
    },
};

const $Audio = document.createElement('audio');

class BaseView extends IView {

    get $audio() {
        return $Audio;
    }

    constructor(...args) {

        super(...args);
        this.by = { money: 5, ratio: 6 };
        this.registerEvent('auto-fit', () => { this.tableObj.fitColumnWidth(); });
        this.ringtons = Entrance.makeRings();
        this.accounts = [new AccountSimple({})].splice(1);
    }

    setAsRington(rington = RingtonSample) {
        this.userRington = this.helper.deepClone(rington);
    }

    /**
     * 关联当前用户的账号
     * @param {Array<AccountSimple>} list 
     */
    attachAccounts(list) {
        this.accounts = list;
    }

    /**
     * 是否至少有一个信用账号
     */
    hasAnyCreditAccount() {
        return this.accounts.some(x => !!x.credit);
    }

    /**
     * 合约是否为可融资标的
     */
    isInstrumentCreditableBuy(instrument) {

        if (!instrument) {
            return false;
        }

        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, instrument, true);
        return matches.length > 0 && matches.some(x => x.creditBuy == 1);
    }

    play(rington, crington) {

        let matched = this.ringtons.find(x => x.code == rington);
        if (matched === undefined) {
            return;
        }

        let murl = !matched.isCustomized ? matched.mediaUrl : crington;
        if (!fs.existsSync(murl)) {
            murl = this.ringtons[0].mediaUrl;
        }

        try {

            if (!this.$audio.paused) {
                this.$audio.pause();
            }
    
            clearTimeout(this.pauseTimer);
            this.$audio.src = murl;
            this.$audio.play();

            /**
             * have to pause anyway after some time > this is not a loving of music but concentrating on trading
             */
            this.pauseTimer = setTimeout(() => { this.$audio.pause(); }, 1000 * 10);
        }
        catch(ex) {
            console.error(ex);
        }
    }

    ring4OrderFirstTrade(isBuy) {

        if (!this.userRington) {
            return;
        }

        let urt = this.userRington;
        let rington = isBuy ? urt.bought : urt.sold;
        let crington = urt.customized ? (isBuy ? urt.customized.bought : urt.customized.sold) : undefined;
        this.play(rington, crington);
    }

    /**
     * @param {BuyTask} task 
     */
    ring4Canceled(task) {

        if (!this.userRington) {
            return;
        }

        let urt = this.userRington;
        this.play(urt.canceled, urt.customized ? urt.customized.canceled : undefined);
    }

    /**
     * @param {BuyTask} task 
     */
    ring4TaskBeenMonitored(task) {

        if (!this.userRington) {
            return;
        }

        let urt = this.userRington;
        this.play(urt.entrusted, urt.customized ? urt.customized.entrusted : undefined);
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    confirm(isRequired, message, callback) {

        if (isRequired) {
            
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => {
                    callback();
                },
            });
        }
        else {
            callback();
        }
    }

    exportSome() {

        var date = new Date().format('yyyyMMdd');
        var time = new Date().format('hhmmss');
        this.tableObj.exportAllRecords(`${this.title}-${this.userInfo.userName}-${date}-${time}`);
    }

    /**
     * @returns {SmartTable}
     */
    createTable() {
        throw new Error('not implemented');
    }

    /**
     * @param {BuyTask} task 
     */
    formatTaskStatus(task) {
        return (TaskStatuses.find(x => x.value == task.status) || {}).label || task.status;
    }

    /**
     * @param {BuyTask} task 
     */
    formatTaskStatusHtml(task) {

        var ref = TaskStatus.started;
        return task.status == ref.value ? `<span class='s-flag' style="color:#fff;background-color:${ref.color};">${ref.label}</span>` : '---';
    }

    formatPrice({ instrument }, price) {
        return typeof price == 'number' ? price.toFixed(BizHelper.getPricePrecision(instrument)) : price;
    }

    /**
     * @param {String} instrument
     */
    shortizeCode(instrument) {
        return instrument.split('.')[1];
    }

    /**
     * @param {String} instrument
     */
    shortizeCodeCol(data, instrument) {
        return this.shortizeCode(instrument);
    }

    /** 任务是否仅加入到表格中，尚未启动过 */
    isTaskCreated(status) {
        return TaskObject.isTaskCreated(status);
    }

    /** 任务是否处于（服务器端已注册）运行状态中 */
    isTaskRunning(status) {
        return TaskObject.isTaskRunning(status);
    }

    /** 任务是否已发生下单，且未完成 */
    isTaskOrdered(status) {
        return TaskObject.isTaskOrdered(status);
    }

    /** 任务是否已暂停运行 */
    isTaskPaused(status) {
        return TaskObject.isTaskPaused(status);
    }

    /** 任务是否已完成 */
    isTaskFinished(status) {
        return TaskObject.isTaskFinished(status);
    }

    /** 任务是否已删除 */
    isTaskDeleted(status) {
        return TaskObject.isTaskDeleted(status);
    }

    /** 任务是否可被删除 */
    isTaskDeletable(status) {
        return TaskObject.isTaskDeletable(status);
    }

    /**
     * @param {ZtStandardStrategy} setting 
     */
    hasNoneStrategyParamSpecified(setting) {

        let { btriggers, ctriggers, limit } = setting;
        let { max, topmost, position } = limit;
        let has_any_buy = btriggers.some(x => x.checked && x.conditions.filter(y => y.threshold > 0).length == x.conditions.length);
        let has_any_cancel = ctriggers.some(x => x.checked && x.conditions.filter(y => y.threshold > 0).length == x.conditions.length);
        var has_any_limit = max.checked && max.volume > 0 || topmost.checked && topmost.volume > 0 || position.amount > 0 || position.percentage > 0;
        
        return !(has_any_buy || has_any_cancel || has_any_limit);
    }

    /**
     * @param {ZtStandardStrategy} setting 
     */
    hasAnyInputError(setting) {

        let result = { isOk: true, error: '' };
        let { btriggers, ctriggers } = setting;
        let error_buy = btriggers.find(x => x.checked && x.conditions.some(y => !(y.threshold > 0)));
        let error_cancel = ctriggers.find(x => x.checked && x.conditions.some(y => !(y.threshold > 0)));

        if (error_buy) {

            result.isOk = false;
            result.error = error_buy.name;
        }
        else if (error_cancel) {

            result.isOk = false;
            result.error = error_cancel.name;
        }

        if (result.isOk) {

            /**
             * 买卖策略参数均验证通过时，再对规模限制参数进行检查
             */

            Object.assign(result, this.checkLimit(setting));
        }
        
        return result;
    }

    /**
     * @param {ZtStandardStrategy} setting 
     */
    checkLimit(setting) {

        let isOk = true;
        let error = '';
        const ref = setting.limit.position;

        if (ref.method == this.by.money && !(ref.amount > 0)) {

            isOk = false;
            error = '买入金额未指定';
        }
        else if (ref.method == this.by.ratio && !(ref.percentage > 0 && ref.percentage <= 100)) {

            isOk = false;
            error = '买入比例不为0-100%';
        }

        return { isOk, error };
    }

    /**
     * @param {BuyTask} task 
     */
    toSubmit(task, request_code, param_checked_required) {

        if (param_checked_required) {

            /**
             * 是否未设置过任何至少一项条件
             */
            if (this.hasNoneStrategyParamSpecified(task.strategy)) {
                return this.interaction.showError('监控启停错误，策略至少需1项有效指定');
            }
            
            let satisfy = this.hasAnyInputError(task.strategy);
            if (!satisfy.isOk) {
                return this.interaction.showError('策略参数项错误：' + satisfy.error);
            }
        }

        let ts = task.strategy;
        let strategy_type_fixed_as = 51;
        let board_strategy = {

            strategyType: strategy_type_fixed_as,
            strategyDelayTime: 0,
        };

        /**
         * 使用动态配置的，买入触发条件，进行扩充
         */
        ts.btriggers.forEach(trg => {

            board_strategy[trg.variable] = trg.checked;
            trg.conditions.forEach(cdt => {
                let thr = cdt.threshold;
                board_strategy[cdt.variable] = typeof thr == 'number' ? thr * cdt.times : thr;
            });
        });

        var cancel_condition = {

            afterLimitTickCount: 0,
            afterLimitTickEnabled: false,
            beforeTradeCancel: 0,
            beforeCancelOpen: false,
            downRate: 0,
            downRateOpen: 0,
            sellOrderVolume: 0,
            sellOrderVolumeOpen: false,
            customDownRate: 0,
            customDownRateTime: 1000,
            customDownRateOpen: false,
            tradeProtectedTime: 0,
            tradeProtectedEnabled: false,
            effectiveTradedAmount: 0,
            supplementTime: 0,
            supplementOrderVolume: 0,
            supplementEnabled: false,
            followCancel: 0,
            followCancelOpen: false,
            hasSupplement: false,
            hasCanceled: false,
        };

        /**
         * 使用动态配置的，撤单触发条件，进行扩充
         */
        ts.ctriggers.forEach(trg => {

            cancel_condition[trg.variable] = trg.checked;
            trg.conditions.forEach(cdt => {
                let thr = cdt.threshold;
                cancel_condition[cdt.variable] = typeof thr == 'number' ? thr * cdt.times : thr;
            });
        });

        var split_detail = {

            enableMax: ts.limit.max.checked,
            max: ts.limit.max.volume,
            decline: ts.limit.decrease.checked,
            enableMost: ts.limit.topmost.checked,
            most: (ts.limit.topmost.volume || 0) * 100,
        };
        
        var structure = {

            id: task.id,
            userId: this.userInfo.userId,
            userName: this.userInfo.userName,
            instrument: task.instrument,
	        instrumentName: null,
            customStrategy: task.strategyName,
            stockLimitType: null,
            direction: this.systemTrdEnum.tradingDirection.buy.code,
            priceFollowType: null,
            orderPrice: null,
            supplementVolume: null,
            supplementOpen: null,
            splitInterval: null,
            splitType: 1,
            creditFlag: ts.limit.creditFlag && this.hasAnyCreditAccount() && this.isInstrumentCreditableBuy(task.instrument),

            limitPositionType: ts.limit.position.method,
            positionPercent: ts.limit.position.method == this.by.ratio ? ts.limit.position.percentage : 0,
            cash: ts.limit.position.method == this.by.money ? ts.limit.position.amount * 10000 : 0,
            strikeBoardStatus: this.helper.isNone(task.id) ? TaskStatus.created.value : task.status,
            
            splitDetail: split_detail,
            boardStrategy: board_strategy,
            cancelCondition: cancel_condition,
        };

        if (this.helper.isNone(structure.id) || request_code == Cm20FunctionCodes.request.start) {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, request_code, structure, task.localId);
        }
        else {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, request_code, structure, 0);
        }

        this.log(`to start/stop/modify a task, task = ${JSON.stringify(structure)}`);
        this.interaction.notify({ message: '参数变动，已提交到服务器', position: 'bottom-right', type: 'success', duration: 1000 });
    }

    build($container) {
        
        super.build($container);
        this.helper.extend(this, ColumnCommonFunc);
        this.tableObj = this.createTable();
    }
}

module.exports = { BaseView };